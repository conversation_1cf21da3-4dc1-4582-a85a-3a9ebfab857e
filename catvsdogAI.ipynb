import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

data = pd.read_csv('cat_dog.csv')

data.head()

data = np.array(data)
m, n = data.shape
np.random.shuffle(data)

m,n

train_data = data[:int(0.8 * m), :]
val_data = data[int(0.8 * m): , :]

# X = all columns except last, y = last column
X_train = train_data[:, :-1].T
X_train = X_train / 255.0  # Normalize pixel values to [0, 1]
y_train = train_data[:, -1]

X_val = val_data[:, :-1].T
X_val = X_val / 255.0  # Normalize pixel values to [0, 1]
y_val = val_data[:, -1]


print("X_train shape:", X_train.shape)
print("y_train shape:", y_train.shape)
print("X_val shape:", X_val.shape)
print("y_val shape:", y_val.shape)