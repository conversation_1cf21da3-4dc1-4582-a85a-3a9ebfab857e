import numpy as np
import matplotlib.pyplot as plt
import pandas as pd

data = pd.read_csv('cat_dog.csv')

print(data.head(5))

data = np.array(data)
m, n = data.shape
np.random.shuffle(data)

print(m,n)

train_data = data[:int(0.8 * m), :]
val_data = data[int(0.8 * m): , :]

X_train = train_data[:, 1:].T
X_train = X_train / 255.0  # Normalize pixel values to [0, 1]
y_train = train_data[:, 0]

X_val = val_data[:, 1:].T
X_val = X_val / 255.0  # Normalize pixel values to [0, 1]
y_val = val_data[:, 0]